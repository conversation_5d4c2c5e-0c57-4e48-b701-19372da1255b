# EGGO数据库注释文件下载脚本

## 概述

这个多线程脚本用于根据metadata-v2.tsv文件中的genome_id或taxid字段从EGGO数据库下载注释文件。脚本会排除GTF注释文件，下载其他所有注释文件，并以genome_id值命名目录和文件。

## 文件说明

- `download_eggo_annotations.py` - 主要的下载脚本
- `test_download.py` - 测试脚本，用于验证功能
- `metadata-v2.tsv` - 输入的metadata文件
- `README.md` - 本说明文档

## 功能特点

1. **多线程下载**: 支持并发下载，提高下载效率
2. **断点续传**: 自动跳过已存在的文件
3. **详细日志**: 记录下载过程和错误信息
4. **进度报告**: 生成详细的下载报告
5. **错误处理**: 优雅处理网络错误和超时

## EGGO数据库文件列表

脚本会下载以下注释文件（排除GTF文件）：

### 核心数据文件
- `EGGO.csv` - 主要的EGGO数据库文件
- `EGGO.RData` - R数据格式的EGGO数据库

### 系统发育和分类信息
- `16S_seqnames.txt` - 16S序列名称
- `16S.cut80.tree` - 16S系统发育树
- `gtdbtk.bac120.summary.tsv` - GTDB细菌分类信息
- `gtdbtk.ar122.summary.tsv` - GTDB古菌分类信息
- `gtdbtk.bac120.classify.tree` - GTDB细菌分类树
- `gtdbtk.ar122.classify.tree` - GTDB古菌分类树
- `gut.gtdbtk.bac120.summary.tsv` - 肠道细菌GTDB分类
- `gut.gtdbtk.ar122.summary.tsv` - 肠道古菌GTDB分类
- `gut.gtdbtk.bac120.classify.tree` - 肠道细菌分类树
- `gut.gtdbtk.ar122.classify.tree` - 肠道古菌分类树

### 功能注释
- `COG_proportions.RData` - COG功能分类比例
- `CAZyDB.07302020.fam-activities.txt` - CAZy数据库家族活性
- `cog-20.def.tab` - COG定义表
- `ar14.arCOGdef.tab` - arCOG定义表
- `fun-20.tab` - 功能分类表
- `ribosomal_protein_COGs.txt` - 核糖体蛋白COG

### 基因组信息
- `condensed_species_NCBI.csv` - NCBI物种信息
- `genes_genomes_hits.txt` - 基因组基因比对结果
- `genome_lengths_vs.txt` - 基因组长度信息
- `genus_genomes.txt` - 属级基因组信息
- `genusgenomes.cogs.gz` - 属级基因组COG注释
- `gc_and_length_vs.tsv` - GC含量和长度信息
- `n_proteins.tbl` - 蛋白质数量表
- `phyla.tbl` - 门级分类表
- `atgcgenomelist.csv` - ATGC基因组列表
- `subsampled_assemblies.txt` - 子采样组装信息

### 密码子和进化分析
- `ATGCCUB.RData` - 密码子使用偏好数据
- `CodonStatistics_Okie.RData` - Okie密码子统计
- `CodonStatistics_RedSea.RData` - 红海密码子统计
- `summary_dNdS_grouped.tbl` - dN/dS分析结果
- `summary_dS_noribo_grouped.tbl` - 非核糖体dS分析
- `summary_dS_ribo_grouped.tbl` - 核糖体dS分析

### 其他数据
- `Ne_Bobay2018.csv` - 有效群体大小数据
- `refseq_filt.caz` - RefSeq过滤的CAZy注释
- `EGGO_16S_BLASTDB.tar.gz` - 16S BLAST数据库
- `function_enrichment_propmixed_significant.csv` - 功能富集分析
- `GORGsize.RData` - GORG大小数据
- `PhiRecombTest.RData` - Phi重组测试数据
- `nearest_neighbors.RData` - 最近邻数据
- `neighbors.RData` - 邻居数据
- `sentivitity_phyloglm.RData` - 敏感性系统发育GLM数据
- `stat_data.RData` - 统计数据
- `stat_data_madin.RData` - Madin统计数据

## 安装依赖

```bash
pip install pandas requests pathlib
```

## 使用方法

### 基本用法

```bash
python download_eggo_annotations.py metadata-v2.tsv
```

### 指定输出目录和线程数

```bash
python download_eggo_annotations.py metadata-v2.tsv eggo_annotations 10
```

### 参数说明

- `metadata_file`: metadata-v2.tsv文件路径（必需）
- `output_dir`: 输出目录（可选，默认: eggo_annotations）
- `max_workers`: 最大线程数（可选，默认: 10）

### 测试脚本

在正式运行前，可以使用测试脚本验证功能：

```bash
python test_download.py metadata-v2.tsv
```

测试脚本只会下载前5个基因组的少量文件，用于验证网络连接和脚本功能。

## 输出结构

```
eggo_annotations/
├── common_annotations/          # 通用注释文件
│   ├── EGGO.csv
│   ├── EGGO.RData
│   └── ...
├── GCA_000219855.1/            # 基因组特定目录
│   ├── EGGO.csv
│   ├── EGGO.RData
│   ├── ...
│   └── GCA_000219855.1_download_report.json
├── GCA_000283575.1/
│   └── ...
├── download_summary.json       # 总体下载报告
└── download_log.txt           # 详细日志文件
```

## 日志和报告

### 日志文件
- `download_log.txt` - 详细的下载日志，包含所有下载过程和错误信息

### 报告文件
- `download_summary.json` - 总体下载报告，包含统计信息和所有基因组的下载状态
- `{genome_id}_download_report.json` - 每个基因组的详细下载报告

### 报告内容示例

```json
{
  "total_genomes": 21572,
  "total_files_attempted": 1078600,
  "successful_downloads": 950000,
  "failed_downloads": 50000,
  "skipped_downloads": 78600,
  "common_files_downloaded": 45,
  "common_files_failed": 5,
  "duration_seconds": 7200,
  "duration_formatted": "2.00 hours"
}
```

## 注意事项

1. **网络连接**: 确保网络连接稳定，某些大文件（如EGGO.csv）可能需要较长时间下载
2. **存储空间**: 确保有足够的磁盘空间，完整下载可能需要数GB空间
3. **线程数**: 根据网络带宽和系统性能调整线程数，过多线程可能导致网络拥塞
4. **断点续传**: 脚本支持断点续传，中断后重新运行会跳过已下载的文件
5. **超时设置**: 默认超时时间为120秒，如果网络较慢可能需要调整

## 故障排除

### 常见问题

1. **下载超时**
   - 检查网络连接
   - 减少并发线程数
   - 增加超时时间（修改脚本中的timeout参数）

2. **权限错误**
   - 确保对输出目录有写权限
   - 检查文件系统空间

3. **依赖缺失**
   - 安装所需的Python包：`pip install pandas requests`

### 错误日志

查看`download_log.txt`文件获取详细的错误信息和下载状态。

## 许可证

本脚本基于MIT许可证发布。EGGO数据库的使用请遵循其原始许可证条款。

## 参考

- EGGO数据库: https://github.com/jlw-ecoevo/eggo
- gRodon包: https://github.com/jlw-ecoevo/gRodon
