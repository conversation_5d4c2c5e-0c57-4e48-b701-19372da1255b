#!/usr/bin/env python3
"""
改进版EGGO数据库注释文件下载脚本
增加重试机制、更好的错误处理和网络优化
"""

import os
import sys
import pandas as pd
import requests
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path
import logging
import json
from urllib3.util.retry import Retry
from requests.adapters import HTTPAdapter

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(threadName)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('download_improved_log.txt'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class EGGOImprovedDownloader:
    def __init__(self, metadata_file, output_dir="eggo_annotations", max_workers=5):
        """
        初始化改进版EGGO注释文件下载器
        """
        self.metadata_file = metadata_file
        self.output_dir = Path(output_dir)
        self.max_workers = max_workers
        
        # 配置带重试机制的session
        self.session = requests.Session()
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # EGGO数据库GitHub仓库基础URL
        self.eggo_base_url = "https://raw.githubusercontent.com/jlw-ecoevo/eggo/master/Data/"
        
        # 创建输出目录
        self.output_dir.mkdir(exist_ok=True)
        
        # 统计信息
        self.download_stats = {
            'total_genomes': 0,
            'total_files': 0,
            'success': 0,
            'failed': 0,
            'skipped': 0
        }
        
        # 线程锁
        self.stats_lock = threading.Lock()
        
        # 按文件大小分类的注释文件列表
        self.small_files = [
            "CAZyDB.07302020.fam-activities.txt",
            "cog-20.def.tab",
            "ar14.arCOGdef.tab", 
            "fun-20.tab",
            "ribosomal_protein_COGs.txt",
            "phyla.tbl",
            "Ne_Bobay2018.csv",
            "function_enrichment_propmixed_significant.csv",
        ]
        
        self.medium_files = [
            "condensed_species_NCBI.csv",
            "genome_lengths_vs.txt",
            "genus_genomes.txt",
            "gc_and_length_vs.tsv",
            "n_proteins.tbl",
            "atgcgenomelist.csv",
            "subsampled_assemblies.txt",
        ]
        
        self.large_files = [
            "EGGO.csv",
            "EGGO.RData",
            "16S_seqnames.txt",
            "16S.cut80.tree",
            "genes_genomes_hits.txt",
            "genusgenomes.cogs.gz",
            "refseq_filt.caz",
            "EGGO_16S_BLASTDB.tar.gz",
            "summary_dNdS_grouped.tbl",
            "summary_dS_noribo_grouped.tbl",
            "summary_dS_ribo_grouped.tbl",
        ]
        
        self.rdata_files = [
            "ATGCCUB.RData",
            "COG_proportions.RData",
            "CodonStatistics_Okie.RData",
            "CodonStatistics_RedSea.RData", 
            "GORGsize.RData",
            "PhiRecombTest.RData",
            "nearest_neighbors.RData",
            "neighbors.RData",
            "sentivitity_phyloglm.RData",
            "stat_data.RData",
            "stat_data_madin.RData"
        ]
        
        self.tree_files = [
            "gtdbtk.bac120.summary.tsv",
            "gtdbtk.ar122.summary.tsv",
            "gtdbtk.bac120.classify.tree",
            "gtdbtk.ar122.classify.tree",
            "gut.gtdbtk.bac120.summary.tsv",
            "gut.gtdbtk.ar122.summary.tsv",
            "gut.gtdbtk.bac120.classify.tree",
            "gut.gtdbtk.ar122.classify.tree",
        ]
        
        # 合并所有文件列表
        self.all_files = (self.small_files + self.medium_files + 
                         self.large_files + self.rdata_files + self.tree_files)
        
    def load_metadata(self, limit=None):
        """加载metadata文件"""
        try:
            df = pd.read_csv(self.metadata_file, sep='\t')
            if limit:
                df = df.head(limit)
            logger.info(f"成功加载metadata文件，共{len(df)}条记录")
            return df
        except Exception as e:
            logger.error(f"加载metadata文件失败: {e}")
            return None
    
    def download_file_with_retry(self, url, output_path, max_retries=3):
        """带重试机制的文件下载"""
        for attempt in range(max_retries):
            try:
                # 根据文件类型设置不同的超时时间
                filename = url.split('/')[-1]
                if filename in self.large_files:
                    timeout = 300  # 5分钟
                elif filename in self.medium_files:
                    timeout = 120  # 2分钟
                else:
                    timeout = 60   # 1分钟
                
                response = self.session.get(url, timeout=timeout, stream=True)
                response.raise_for_status()
                
                # 创建目录
                output_path.parent.mkdir(parents=True, exist_ok=True)
                
                # 下载文件
                with open(output_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                
                return True, None
                
            except requests.exceptions.Timeout as e:
                logger.warning(f"下载超时 (尝试 {attempt + 1}/{max_retries}): {filename}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # 指数退避
                    continue
                return False, f"超时: {str(e)}"
                
            except requests.exceptions.RequestException as e:
                logger.warning(f"下载错误 (尝试 {attempt + 1}/{max_retries}): {filename} - {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)
                    continue
                return False, str(e)
                
            except Exception as e:
                logger.error(f"未知错误: {filename} - {str(e)}")
                return False, str(e)
        
        return False, "重试次数已用完"
    
    def process_genome(self, genome_id, taxid, file_list=None):
        """处理单个基因组的注释文件下载"""
        genome_dir = self.output_dir / genome_id
        genome_dir.mkdir(exist_ok=True)
        
        # 如果没有指定文件列表，使用所有文件
        if file_list is None:
            file_list = self.all_files
        
        downloaded_files = []
        failed_files = []
        skipped_files = []
        
        for filename in file_list:
            url = self.eggo_base_url + filename
            output_path = genome_dir / filename
            
            # 跳过已存在的文件
            if output_path.exists():
                logger.info(f"文件已存在，跳过: {output_path}")
                skipped_files.append(filename)
                continue
            
            logger.info(f"正在下载: {filename}")
            success, error = self.download_file_with_retry(url, output_path)
            
            if success:
                downloaded_files.append(filename)
                logger.info(f"下载成功: {filename} -> {genome_id}/")
            else:
                failed_files.append((filename, error))
                logger.warning(f"下载失败: {filename} - {error}")
        
        # 更新统计信息
        with self.stats_lock:
            self.download_stats['success'] += len(downloaded_files)
            self.download_stats['failed'] += len(failed_files)
            self.download_stats['skipped'] += len(skipped_files)
        
        # 创建下载报告
        report = {
            'genome_id': genome_id,
            'taxid': taxid,
            'downloaded_files': downloaded_files,
            'failed_files': failed_files,
            'skipped_files': skipped_files,
            'download_time': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # 保存报告到JSON文件
        report_file = genome_dir / f"{genome_id}_download_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        return report

    def download_common_files(self, file_list=None):
        """下载通用的EGGO注释文件到公共目录"""
        common_dir = self.output_dir / "common_annotations"
        common_dir.mkdir(exist_ok=True)

        if file_list is None:
            file_list = self.all_files

        logger.info("开始下载通用EGGO注释文件...")

        downloaded_files = []
        failed_files = []

        for filename in file_list:
            url = self.eggo_base_url + filename
            output_path = common_dir / filename

            # 跳过已存在的文件
            if output_path.exists():
                logger.info(f"通用文件已存在，跳过: {filename}")
                continue

            logger.info(f"正在下载通用文件: {filename}")
            success, error = self.download_file_with_retry(url, output_path)

            if success:
                downloaded_files.append(filename)
                logger.info(f"通用文件下载成功: {filename}")
            else:
                failed_files.append((filename, error))
                logger.warning(f"通用文件下载失败: {filename} - {error}")

        return downloaded_files, failed_files

    def run(self, mode="full", limit=None):
        """运行下载任务"""
        logger.info(f"开始EGGO注释文件下载任务 (模式: {mode})")

        # 加载metadata
        metadata_df = self.load_metadata(limit)
        if metadata_df is None:
            return False

        # 根据模式选择文件列表
        if mode == "test":
            file_list = self.small_files[:5]  # 只下载5个小文件进行测试
        elif mode == "small":
            file_list = self.small_files
        elif mode == "medium":
            file_list = self.small_files + self.medium_files
        else:  # full
            file_list = self.all_files

        # 首先下载通用注释文件
        common_downloaded, common_failed = self.download_common_files(file_list)

        # 准备基因组特定的下载任务
        tasks = []
        for _, row in metadata_df.iterrows():
            genome_id = row['genome_id']
            taxid = row['taxid']
            tasks.append((genome_id, taxid))

        self.download_stats['total_genomes'] = len(tasks)
        self.download_stats['total_files'] = len(tasks) * len(file_list)
        logger.info(f"准备为{len(tasks)}个基因组下载{len(file_list)}个注释文件")

        # 使用线程池执行下载
        start_time = time.time()
        reports = []

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交任务
            future_to_genome = {
                executor.submit(self.process_genome, genome_id, taxid, file_list): (genome_id, taxid)
                for genome_id, taxid in tasks
            }

            # 处理完成的任务
            for future in as_completed(future_to_genome):
                genome_id, taxid = future_to_genome[future]
                try:
                    report = future.result()
                    reports.append(report)
                    logger.info(f"完成处理基因组: {genome_id}")
                except Exception as e:
                    logger.error(f"处理基因组{genome_id}时出错: {e}")
                    with self.stats_lock:
                        self.download_stats['failed'] += len(file_list)

        # 生成总体报告
        end_time = time.time()
        duration = end_time - start_time

        summary_report = {
            'mode': mode,
            'total_genomes': self.download_stats['total_genomes'],
            'total_files_attempted': self.download_stats['total_files'],
            'successful_downloads': self.download_stats['success'],
            'failed_downloads': self.download_stats['failed'],
            'skipped_downloads': self.download_stats['skipped'],
            'common_files_downloaded': len(common_downloaded),
            'common_files_failed': len(common_failed),
            'duration_seconds': duration,
            'duration_formatted': f"{duration/3600:.2f} hours",
            'file_list': file_list,
            'common_downloaded_files': common_downloaded,
            'common_failed_files': common_failed,
            'genome_reports': reports
        }

        # 保存总体报告
        summary_file = self.output_dir / f"download_summary_{mode}.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary_report, f, indent=2, ensure_ascii=False)

        logger.info(f"下载任务完成！")
        logger.info(f"模式: {mode}")
        logger.info(f"总计基因组: {self.download_stats['total_genomes']} 个")
        logger.info(f"成功下载: {self.download_stats['success']} 个文件")
        logger.info(f"下载失败: {self.download_stats['failed']} 个文件")
        logger.info(f"跳过文件: {self.download_stats['skipped']} 个文件")
        logger.info(f"通用文件下载: {len(common_downloaded)} 个成功, {len(common_failed)} 个失败")
        logger.info(f"耗时: {duration/3600:.2f} 小时")

        return True

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python download_eggo_improved.py <metadata_file> [output_dir] [max_workers] [mode] [limit]")
        print("示例: python download_eggo_improved.py metadata-v2.tsv eggo_annotations 5 test 10")
        print("\n参数说明:")
        print("  metadata_file: metadata-v2.tsv文件路径")
        print("  output_dir: 输出目录（默认: eggo_annotations）")
        print("  max_workers: 最大线程数（默认: 5）")
        print("  mode: 下载模式（test/small/medium/full，默认: full）")
        print("  limit: 限制处理的基因组数量（可选）")
        print("\n模式说明:")
        print("  test: 只下载5个小文件进行测试")
        print("  small: 下载小文件（快速完成）")
        print("  medium: 下载小文件和中等文件")
        print("  full: 下载所有文件（完整数据集）")
        sys.exit(1)

    metadata_file = sys.argv[1]
    output_dir = sys.argv[2] if len(sys.argv) > 2 else "eggo_annotations"
    max_workers = int(sys.argv[3]) if len(sys.argv) > 3 else 5
    mode = sys.argv[4] if len(sys.argv) > 4 else "full"
    limit = int(sys.argv[5]) if len(sys.argv) > 5 else None

    if not os.path.exists(metadata_file):
        print(f"错误: 找不到metadata文件: {metadata_file}")
        sys.exit(1)

    if mode not in ["test", "small", "medium", "full"]:
        print(f"错误: 无效的模式 '{mode}'，请选择: test, small, medium, full")
        sys.exit(1)

    # 创建下载器并运行
    downloader = EGGOImprovedDownloader(metadata_file, output_dir, max_workers)
    success = downloader.run(mode, limit)

    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
