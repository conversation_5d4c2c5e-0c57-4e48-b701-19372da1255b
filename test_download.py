#!/usr/bin/env python3
"""
测试脚本：下载少量EGGO注释文件进行测试
"""

import os
import sys
import pandas as pd
import requests
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path
import logging
import json

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(threadName)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_download_log.txt'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class EGGOTestDownloader:
    def __init__(self, metadata_file, output_dir="test_eggo_annotations", max_workers=5):
        """
        初始化EGGO测试下载器
        """
        self.metadata_file = metadata_file
        self.output_dir = Path(output_dir)
        self.max_workers = max_workers
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # EGGO数据库GitHub仓库基础URL
        self.eggo_base_url = "https://raw.githubusercontent.com/jlw-ecoevo/eggo/master/Data/"
        
        # 创建输出目录
        self.output_dir.mkdir(exist_ok=True)
        
        # 统计信息
        self.download_stats = {
            'total_genomes': 0,
            'success': 0,
            'failed': 0,
            'skipped': 0
        }
        
        # 线程锁
        self.stats_lock = threading.Lock()
        
        # 测试用的少量注释文件
        self.test_files = [
            "EGGO.csv",                           # 主要的EGGO数据库文件
            "CAZyDB.07302020.fam-activities.txt", # CAZy数据库家族活性
            "condensed_species_NCBI.csv",         # NCBI物种信息
            "cog-20.def.tab",                     # COG定义表
            "fun-20.tab",                         # 功能分类表
        ]
        
    def load_metadata(self):
        """加载metadata文件"""
        try:
            df = pd.read_csv(self.metadata_file, sep='\t')
            # 只取前5行进行测试
            df = df.head(5)
            logger.info(f"成功加载metadata文件，测试用{len(df)}条记录")
            return df
        except Exception as e:
            logger.error(f"加载metadata文件失败: {e}")
            return None
    
    def download_file(self, url, output_path):
        """下载单个文件"""
        try:
            response = self.session.get(url, timeout=60, stream=True)
            response.raise_for_status()
            
            # 创建目录
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 下载文件
            with open(output_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            
            return True, None
            
        except requests.exceptions.RequestException as e:
            return False, str(e)
        except Exception as e:
            return False, str(e)
    
    def process_genome(self, genome_id, taxid):
        """处理单个基因组的注释文件下载"""
        genome_dir = self.output_dir / genome_id
        genome_dir.mkdir(exist_ok=True)
        
        downloaded_files = []
        failed_files = []
        skipped_files = []
        
        for filename in self.test_files:
            url = self.eggo_base_url + filename
            output_path = genome_dir / filename
            
            # 跳过已存在的文件
            if output_path.exists():
                logger.info(f"文件已存在，跳过: {output_path}")
                skipped_files.append(filename)
                continue
            
            logger.info(f"正在下载: {url}")
            success, error = self.download_file(url, output_path)
            
            if success:
                downloaded_files.append(filename)
                logger.info(f"下载成功: {filename} -> {genome_id}/")
            else:
                failed_files.append((filename, error))
                logger.warning(f"下载失败: {filename} - {error}")
        
        # 更新统计信息
        with self.stats_lock:
            self.download_stats['success'] += len(downloaded_files)
            self.download_stats['failed'] += len(failed_files)
            self.download_stats['skipped'] += len(skipped_files)
        
        # 创建下载报告
        report = {
            'genome_id': genome_id,
            'taxid': taxid,
            'downloaded_files': downloaded_files,
            'failed_files': failed_files,
            'skipped_files': skipped_files,
            'download_time': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # 保存报告到JSON文件
        report_file = genome_dir / f"{genome_id}_test_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        return report
    
    def run(self):
        """运行测试下载任务"""
        logger.info("开始EGGO注释文件测试下载")
        
        # 加载metadata
        metadata_df = self.load_metadata()
        if metadata_df is None:
            return False
        
        # 准备下载任务
        tasks = []
        for _, row in metadata_df.iterrows():
            genome_id = row['genome_id']
            taxid = row['taxid']
            tasks.append((genome_id, taxid))
        
        self.download_stats['total_genomes'] = len(tasks)
        logger.info(f"准备为{len(tasks)}个基因组下载测试注释文件")
        
        # 使用线程池执行下载
        start_time = time.time()
        reports = []
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交任务
            future_to_genome = {
                executor.submit(self.process_genome, genome_id, taxid): (genome_id, taxid)
                for genome_id, taxid in tasks
            }
            
            # 处理完成的任务
            for future in as_completed(future_to_genome):
                genome_id, taxid = future_to_genome[future]
                try:
                    report = future.result()
                    reports.append(report)
                    logger.info(f"完成处理基因组: {genome_id}")
                except Exception as e:
                    logger.error(f"处理基因组{genome_id}时出错: {e}")
        
        # 生成总体报告
        end_time = time.time()
        duration = end_time - start_time
        
        summary_report = {
            'total_genomes': self.download_stats['total_genomes'],
            'successful_downloads': self.download_stats['success'],
            'failed_downloads': self.download_stats['failed'],
            'skipped_downloads': self.download_stats['skipped'],
            'duration_seconds': duration,
            'duration_formatted': f"{duration:.2f} seconds",
            'genome_reports': reports
        }
        
        # 保存总体报告
        summary_file = self.output_dir / "test_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary_report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"测试下载完成！")
        logger.info(f"总计基因组: {self.download_stats['total_genomes']} 个")
        logger.info(f"成功下载: {self.download_stats['success']} 个文件")
        logger.info(f"下载失败: {self.download_stats['failed']} 个文件")
        logger.info(f"跳过文件: {self.download_stats['skipped']} 个文件")
        logger.info(f"耗时: {duration:.2f} 秒")
        
        return True

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python test_download.py <metadata_file> [output_dir] [max_workers]")
        print("示例: python test_download.py metadata-v2.tsv test_eggo_annotations 5")
        sys.exit(1)
    
    metadata_file = sys.argv[1]
    output_dir = sys.argv[2] if len(sys.argv) > 2 else "test_eggo_annotations"
    max_workers = int(sys.argv[3]) if len(sys.argv) > 3 else 5
    
    if not os.path.exists(metadata_file):
        print(f"错误: 找不到metadata文件: {metadata_file}")
        sys.exit(1)
    
    # 创建下载器并运行
    downloader = EGGOTestDownloader(metadata_file, output_dir, max_workers)
    success = downloader.run()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
