#!/usr/bin/env python3
"""
多线程脚本：根据metadata-v2.tsv文件中的genome_id或taxid字段从EGGO数据库下载注释文件
排除GTF注释文件，下载其他所有注释文件，并以genome_id值命名目录和文件
"""

import os
import sys
import pandas as pd
import requests
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path
import logging
import json

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(threadName)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('download_log.txt'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class EGGOAnnotationDownloader:
    def __init__(self, metadata_file, output_dir="eggo_annotations", max_workers=10):
        """
        初始化EGGO注释文件下载器
        
        Args:
            metadata_file: metadata-v2.tsv文件路径
            output_dir: 输出目录
            max_workers: 最大线程数
        """
        self.metadata_file = metadata_file
        self.output_dir = Path(output_dir)
        self.max_workers = max_workers
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # EGGO数据库GitHub仓库基础URL
        self.eggo_base_url = "https://raw.githubusercontent.com/jlw-ecoevo/eggo/master/Data/"
        
        # 创建输出目录
        self.output_dir.mkdir(exist_ok=True)
        
        # 统计信息
        self.download_stats = {
            'total_genomes': 0,
            'total_files': 0,
            'success': 0,
            'failed': 0,
            'skipped': 0
        }
        
        # 线程锁
        self.stats_lock = threading.Lock()
        
        # EGGO数据库中的注释文件列表（排除GTF文件）
        self.annotation_files = [
            "EGGO.csv",                           # 主要的EGGO数据库文件
            "EGGO.RData",                         # R数据格式的EGGO数据库
            "16S_seqnames.txt",                   # 16S序列名称
            "16S.cut80.tree",                     # 16S系统发育树
            "ATGCCUB.RData",                      # 密码子使用偏好数据
            "COG_proportions.RData",              # COG功能分类比例
            "CAZyDB.07302020.fam-activities.txt", # CAZy数据库家族活性
            "condensed_species_NCBI.csv",         # NCBI物种信息
            "gtdbtk.bac120.summary.tsv",          # GTDB细菌分类信息
            "gtdbtk.ar122.summary.tsv",           # GTDB古菌分类信息
            "gtdbtk.bac120.classify.tree",        # GTDB细菌分类树
            "gtdbtk.ar122.classify.tree",         # GTDB古菌分类树
            "gut.gtdbtk.bac120.summary.tsv",      # 肠道细菌GTDB分类
            "gut.gtdbtk.ar122.summary.tsv",       # 肠道古菌GTDB分类
            "gut.gtdbtk.bac120.classify.tree",    # 肠道细菌分类树
            "gut.gtdbtk.ar122.classify.tree",     # 肠道古菌分类树
            "cog-20.def.tab",                     # COG定义表
            "ar14.arCOGdef.tab",                  # arCOG定义表
            "fun-20.tab",                         # 功能分类表
            "ribosomal_protein_COGs.txt",         # 核糖体蛋白COG
            "genes_genomes_hits.txt",             # 基因组基因比对结果
            "genome_lengths_vs.txt",              # 基因组长度信息
            "genus_genomes.txt",                  # 属级基因组信息
            "genusgenomes.cogs.gz",               # 属级基因组COG注释
            "gc_and_length_vs.tsv",               # GC含量和长度信息
            "n_proteins.tbl",                     # 蛋白质数量表
            "phyla.tbl",                          # 门级分类表
            "atgcgenomelist.csv",                 # ATGC基因组列表
            "subsampled_assemblies.txt",          # 子采样组装信息
            "Ne_Bobay2018.csv",                   # 有效群体大小数据
            "refseq_filt.caz",                    # RefSeq过滤的CAZy注释
            "EGGO_16S_BLASTDB.tar.gz",           # 16S BLAST数据库
            "summary_dNdS_grouped.tbl",           # dN/dS分析结果
            "summary_dS_noribo_grouped.tbl",      # 非核糖体dS分析
            "summary_dS_ribo_grouped.tbl",        # 核糖体dS分析
            "function_enrichment_propmixed_significant.csv", # 功能富集分析
            "CodonStatistics_Okie.RData",
            "CodonStatistics_RedSea.RData", 
            "GORGsize.RData",
            "PhiRecombTest.RData",
            "nearest_neighbors.RData",
            "neighbors.RData",
            "sentivitity_phyloglm.RData",
            "stat_data.RData",
            "stat_data_madin.RData"
        ]
        
    def load_metadata(self):
        """加载metadata文件"""
        try:
            df = pd.read_csv(self.metadata_file, sep='	')
            logger.info(f"成功加载metadata文件，共{len(df)}条记录")
            return df
        except Exception as e:
            logger.error(f"加载metadata文件失败: {e}")
            return None
    
    def download_file(self, url, output_path):
        """下载单个文件"""
        try:
            response = self.session.get(url, timeout=120, stream=True)
            response.raise_for_status()
            
            # 创建目录
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 下载文件
            with open(output_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            
            return True, None
            
        except requests.exceptions.RequestException as e:
            return False, str(e)
        except Exception as e:
            return False, str(e)

    def process_genome(self, genome_id, taxid):
        """处理单个基因组的注释文件下载"""
        genome_dir = self.output_dir / genome_id
        genome_dir.mkdir(exist_ok=True)

        downloaded_files = []
        failed_files = []
        skipped_files = []

        for filename in self.annotation_files:
            url = self.eggo_base_url + filename
            output_path = genome_dir / filename

            # 跳过已存在的文件
            if output_path.exists():
                logger.info(f"文件已存在，跳过: {output_path}")
                skipped_files.append(filename)
                continue

            logger.info(f"正在下载: {url}")
            success, error = self.download_file(url, output_path)

            if success:
                downloaded_files.append(filename)
                logger.info(f"下载成功: {filename} -> {genome_id}/")
            else:
                failed_files.append((filename, error))
                logger.warning(f"下载失败: {filename} - {error}")

        # 更新统计信息
        with self.stats_lock:
            self.download_stats['success'] += len(downloaded_files)
            self.download_stats['failed'] += len(failed_files)
            self.download_stats['skipped'] += len(skipped_files)

        # 创建下载报告
        report = {
            'genome_id': genome_id,
            'taxid': taxid,
            'downloaded_files': downloaded_files,
            'failed_files': failed_files,
            'skipped_files': skipped_files,
            'download_time': time.strftime('%Y-%m-%d %H:%M:%S')
        }

        # 保存报告到JSON文件
        report_file = genome_dir / f"{genome_id}_download_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        return report

    def download_common_files(self):
        """下载通用的EGGO注释文件到公共目录"""
        common_dir = self.output_dir / "common_annotations"
        common_dir.mkdir(exist_ok=True)

        logger.info("开始下载通用EGGO注释文件...")

        downloaded_files = []
        failed_files = []

        for filename in self.annotation_files:
            url = self.eggo_base_url + filename
            output_path = common_dir / filename

            # 跳过已存在的文件
            if output_path.exists():
                logger.info(f"通用文件已存在，跳过: {filename}")
                continue

            logger.info(f"正在下载通用文件: {url}")
            success, error = self.download_file(url, output_path)

            if success:
                downloaded_files.append(filename)
                logger.info(f"通用文件下载成功: {filename}")
            else:
                failed_files.append((filename, error))
                logger.warning(f"通用文件下载失败: {filename} - {error}")

        return downloaded_files, failed_files

    def run(self):
        """运行下载任务"""
        logger.info("开始EGGO注释文件下载任务")

        # 加载metadata
        metadata_df = self.load_metadata()
        if metadata_df is None:
            return False

        # 首先下载通用注释文件
        common_downloaded, common_failed = self.download_common_files()

        # 准备基因组特定的下载任务
        tasks = []
        for _, row in metadata_df.iterrows():
            genome_id = row['genome_id']
            taxid = row['taxid']
            tasks.append((genome_id, taxid))

        self.download_stats['total_genomes'] = len(tasks)
        self.download_stats['total_files'] = len(tasks) * len(self.annotation_files)
        logger.info(f"准备为{len(tasks)}个基因组下载注释文件")

        # 使用线程池执行下载
        start_time = time.time()
        reports = []

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交任务
            future_to_genome = {
                executor.submit(self.process_genome, genome_id, taxid): (genome_id, taxid)
                for genome_id, taxid in tasks
            }

            # 处理完成的任务
            for future in as_completed(future_to_genome):
                genome_id, taxid = future_to_genome[future]
                try:
                    report = future.result()
                    reports.append(report)
                    logger.info(f"完成处理基因组: {genome_id}")
                except Exception as e:
                    logger.error(f"处理基因组{genome_id}时出错: {e}")
                    with self.stats_lock:
                        self.download_stats['failed'] += len(self.annotation_files)

        # 生成总体报告
        end_time = time.time()
        duration = end_time - start_time

        summary_report = {
            'total_genomes': self.download_stats['total_genomes'],
            'total_files_attempted': self.download_stats['total_files'],
            'successful_downloads': self.download_stats['success'],
            'failed_downloads': self.download_stats['failed'],
            'skipped_downloads': self.download_stats['skipped'],
            'common_files_downloaded': len(common_downloaded),
            'common_files_failed': len(common_failed),
            'duration_seconds': duration,
            'duration_formatted': f"{duration/3600:.2f} hours",
            'common_downloaded_files': common_downloaded,
            'common_failed_files': common_failed,
            'genome_reports': reports
        }

        # 保存总体报告
        summary_file = self.output_dir / "download_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary_report, f, indent=2, ensure_ascii=False)

        logger.info(f"下载任务完成！")
        logger.info(f"总计基因组: {self.download_stats['total_genomes']} 个")
        logger.info(f"成功下载: {self.download_stats['success']} 个文件")
        logger.info(f"下载失败: {self.download_stats['failed']} 个文件")
        logger.info(f"跳过文件: {self.download_stats['skipped']} 个文件")
        logger.info(f"通用文件下载: {len(common_downloaded)} 个成功, {len(common_failed)} 个失败")
        logger.info(f"耗时: {duration/3600:.2f} 小时")

        return True

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python download_eggo_annotations.py <metadata_file> [output_dir] [max_workers]")
        print("示例: python download_eggo_annotations.py metadata-v2.tsv eggo_annotations 10")
        print("\n说明:")
        print("  metadata_file: metadata-v2.tsv文件路径")
        print("  output_dir: 输出目录（默认: eggo_annotations）")
        print("  max_workers: 最大线程数（默认: 10）")
        sys.exit(1)

    metadata_file = sys.argv[1]
    output_dir = sys.argv[2] if len(sys.argv) > 2 else "eggo_annotations"
    max_workers = int(sys.argv[3]) if len(sys.argv) > 3 else 10

    if not os.path.exists(metadata_file):
        print(f"错误: 找不到metadata文件: {metadata_file}")
        sys.exit(1)

    # 创建下载器并运行
    downloader = EGGOAnnotationDownloader(metadata_file, output_dir, max_workers)
    success = downloader.run()

    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
