#!/usr/bin/env python3
"""
多线程脚本：根据metadata-v2.tsv文件中的genome_id或taxid字段从EGGO数据库下载注释文件
排除GTF注释文件，下载其他所有注释文件，并以genome_id值命名目录和文件
"""

import os
import sys
import pandas as pd
import requests
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path
import logging
from urllib.parse import urljoin
import json

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(threadName)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('download_log.txt'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class EGGODownloader:
    def __init__(self, metadata_file, output_dir="eggo_annotations", max_workers=10):
        """
        初始化EGGO下载器
        
        Args:
            metadata_file: metadata-v2.tsv文件路径
            output_dir: 输出目录
            max_workers: 最大线程数
        """
        self.metadata_file = metadata_file
        self.output_dir = Path(output_dir)
        self.max_workers = max_workers
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # EGGO数据库相关URL
        self.eggo_base_url = "https://raw.githubusercontent.com/jlw-ecoevo/eggo/master/Data/"
        self.eggo_data_url = "https://raw.githubusercontent.com/jlw-ecoevo/eggo/master/Data/EGGO.csv"
        
        # 创建输出目录
        self.output_dir.mkdir(exist_ok=True)
        
        # 统计信息
        self.download_stats = {
            'total': 0,
            'success': 0,
            'failed': 0,
            'skipped': 0
        }
        
        # 线程锁
        self.stats_lock = threading.Lock()
        
    def load_metadata(self):
        """加载metadata文件"""
        try:
            df = pd.read_csv(self.metadata_file, sep='\t')
            logger.info(f"成功加载metadata文件，共{len(df)}条记录")
            return df
        except Exception as e:
            logger.error(f"加载metadata文件失败: {e}")
            return None
    
    def load_eggo_data(self):
        """下载并加载EGGO数据库"""
        try:
            logger.info("正在下载EGGO数据库...")
            response = self.session.get(self.eggo_data_url, timeout=30)
            response.raise_for_status()
            
            # 保存EGGO数据到本地
            eggo_file = self.output_dir / "EGGO_database.csv"
            with open(eggo_file, 'w', encoding='utf-8') as f:
                f.write(response.text)
            
            # 读取EGGO数据
            eggo_df = pd.read_csv(eggo_file)
            logger.info(f"成功下载EGGO数据库，共{len(eggo_df)}条记录")
            return eggo_df
            
        except Exception as e:
            logger.error(f"下载EGGO数据库失败: {e}")
            return None
    
    def get_annotation_urls(self, genome_id):
        """
        根据genome_id获取可能的注释文件URL列表
        这里我们构建常见的注释文件URL模式
        """
        urls = []
        base_patterns = [
            # NCBI RefSeq/GenBank 常见注释文件格式
            f"https://ftp.ncbi.nlm.nih.gov/genomes/all/{genome_id[:3]}/{genome_id[4:7]}/{genome_id[8:11]}/{genome_id[12:15]}/{genome_id}",
            # 其他可能的数据源
            f"https://ftp.ncbi.nlm.nih.gov/genomes/refseq/bacteria/{genome_id}",
            f"https://ftp.ncbi.nlm.nih.gov/genomes/genbank/bacteria/{genome_id}",
        ]
        
        # 常见注释文件后缀（排除GTF）
        annotation_suffixes = [
            "_genomic.fna.gz",      # 基因组序列
            "_protein.faa.gz",      # 蛋白质序列
            "_cds_from_genomic.fna.gz",  # CDS序列
            "_rna_from_genomic.fna.gz",  # RNA序列
            "_genomic.gff.gz",      # GFF注释文件
            "_feature_table.txt.gz", # 特征表
            "_assembly_report.txt",  # 组装报告
            "_assembly_stats.txt",   # 组装统计
            ".gbff.gz",             # GenBank格式
            "_translated_cds.faa.gz", # 翻译的CDS
        ]
        
        for base_url in base_patterns:
            for suffix in annotation_suffixes:
                urls.append(f"{base_url}/{genome_id}{suffix}")
        
        return urls
    
    def download_file(self, url, output_path):
        """下载单个文件"""
        try:
            response = self.session.get(url, timeout=60, stream=True)
            response.raise_for_status()
            
            # 创建目录
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 下载文件
            with open(output_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            
            return True, None
            
        except requests.exceptions.RequestException as e:
            return False, str(e)
        except Exception as e:
            return False, str(e)
    
    def process_genome(self, genome_id, taxid):
        """处理单个基因组的下载"""
        genome_dir = self.output_dir / genome_id
        genome_dir.mkdir(exist_ok=True)
        
        # 获取注释文件URL列表
        urls = self.get_annotation_urls(genome_id)
        
        downloaded_files = []
        failed_files = []
        
        for url in urls:
            filename = url.split('/')[-1]
            output_path = genome_dir / filename
            
            # 跳过已存在的文件
            if output_path.exists():
                logger.info(f"文件已存在，跳过: {output_path}")
                continue
            
            logger.info(f"正在下载: {url}")
            success, error = self.download_file(url, output_path)
            
            if success:
                downloaded_files.append(filename)
                logger.info(f"下载成功: {filename}")
            else:
                failed_files.append((filename, error))
                logger.warning(f"下载失败: {filename} - {error}")
        
        # 更新统计信息
        with self.stats_lock:
            self.download_stats['success'] += len(downloaded_files)
            self.download_stats['failed'] += len(failed_files)
        
        # 创建下载报告
        report = {
            'genome_id': genome_id,
            'taxid': taxid,
            'downloaded_files': downloaded_files,
            'failed_files': failed_files,
            'download_time': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # 保存报告到JSON文件
        report_file = genome_dir / f"{genome_id}_download_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        return report
    
    def run(self):
        """运行下载任务"""
        logger.info("开始EGGO注释文件下载任务")
        
        # 加载metadata
        metadata_df = self.load_metadata()
        if metadata_df is None:
            return False
        
        # 下载EGGO数据库
        eggo_df = self.load_eggo_data()
        if eggo_df is None:
            logger.warning("无法下载EGGO数据库，将继续使用metadata中的信息")
        
        # 准备下载任务
        tasks = []
        for _, row in metadata_df.iterrows():
            genome_id = row['genome_id']
            taxid = row['taxid']
            tasks.append((genome_id, taxid))
        
        self.download_stats['total'] = len(tasks)
        logger.info(f"准备下载{len(tasks)}个基因组的注释文件")
        
        # 使用线程池执行下载
        start_time = time.time()
        reports = []
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交任务
            future_to_genome = {
                executor.submit(self.process_genome, genome_id, taxid): (genome_id, taxid)
                for genome_id, taxid in tasks
            }
            
            # 处理完成的任务
            for future in as_completed(future_to_genome):
                genome_id, taxid = future_to_genome[future]
                try:
                    report = future.result()
                    reports.append(report)
                    logger.info(f"完成处理基因组: {genome_id}")
                except Exception as e:
                    logger.error(f"处理基因组{genome_id}时出错: {e}")
                    with self.stats_lock:
                        self.download_stats['failed'] += 1
        
        # 生成总体报告
        end_time = time.time()
        duration = end_time - start_time
        
        summary_report = {
            'total_genomes': self.download_stats['total'],
            'successful_downloads': self.download_stats['success'],
            'failed_downloads': self.download_stats['failed'],
            'skipped_downloads': self.download_stats['skipped'],
            'duration_seconds': duration,
            'duration_formatted': f"{duration/3600:.2f} hours",
            'reports': reports
        }
        
        # 保存总体报告
        summary_file = self.output_dir / "download_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary_report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"下载任务完成！")
        logger.info(f"总计: {self.download_stats['total']} 个基因组")
        logger.info(f"成功: {self.download_stats['success']} 个文件")
        logger.info(f"失败: {self.download_stats['failed']} 个文件")
        logger.info(f"耗时: {duration/3600:.2f} 小时")
        
        return True

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python download_eggo_annotations.py <metadata_file> [output_dir] [max_workers]")
        print("示例: python download_eggo_annotations.py metadata-v2.tsv eggo_annotations 10")
        sys.exit(1)
    
    metadata_file = sys.argv[1]
    output_dir = sys.argv[2] if len(sys.argv) > 2 else "eggo_annotations"
    max_workers = int(sys.argv[3]) if len(sys.argv) > 3 else 10
    
    if not os.path.exists(metadata_file):
        print(f"错误: 找不到metadata文件: {metadata_file}")
        sys.exit(1)
    
    # 创建下载器并运行
    downloader = EGGODownloader(metadata_file, output_dir, max_workers)
    success = downloader.run()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
