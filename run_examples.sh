#!/bin/bash
# EGGO数据库下载脚本运行示例

echo "=== EGGO数据库注释文件下载脚本运行示例 ==="
echo ""

# 检查metadata文件是否存在
if [ ! -f "metadata-v2.tsv" ]; then
    echo "错误: 找不到metadata-v2.tsv文件"
    echo "请确保metadata-v2.tsv文件在当前目录中"
    exit 1
fi

# 检查Python脚本是否存在
if [ ! -f "download_eggo_improved.py" ]; then
    echo "错误: 找不到download_eggo_improved.py文件"
    exit 1
fi

echo "找到必要文件，开始运行示例..."
echo ""

# 示例1: 测试模式 - 验证脚本功能
echo "=== 示例1: 测试模式 ==="
echo "下载3个基因组的5个小文件进行测试"
echo "命令: python3 download_eggo_improved.py metadata-v2.tsv example_test 3 test 3"
echo ""
read -p "是否运行测试示例? (y/n): " run_test

if [ "$run_test" = "y" ] || [ "$run_test" = "Y" ]; then
    echo "开始运行测试..."
    python3 download_eggo_improved.py metadata-v2.tsv example_test 3 test 3
    echo ""
    echo "测试完成！检查 example_test/ 目录查看下载的文件"
    echo ""
fi

# 示例2: 小文件模式
echo "=== 示例2: 小文件模式 ==="
echo "下载10个基因组的小文件（快速完成）"
echo "命令: python3 download_eggo_improved.py metadata-v2.tsv example_small 5 small 10"
echo ""
read -p "是否运行小文件示例? (y/n): " run_small

if [ "$run_small" = "y" ] || [ "$run_small" = "Y" ]; then
    echo "开始运行小文件下载..."
    python3 download_eggo_improved.py metadata-v2.tsv example_small 5 small 10
    echo ""
    echo "小文件下载完成！检查 example_small/ 目录"
    echo ""
fi

# 示例3: 中等模式
echo "=== 示例3: 中等模式 ==="
echo "下载50个基因组的小文件和中等文件"
echo "命令: python3 download_eggo_improved.py metadata-v2.tsv example_medium 8 medium 50"
echo ""
read -p "是否运行中等模式示例? (y/n): " run_medium

if [ "$run_medium" = "y" ] || [ "$run_medium" = "Y" ]; then
    echo "开始运行中等模式下载..."
    python3 download_eggo_improved.py metadata-v2.tsv example_medium 8 medium 50
    echo ""
    echo "中等模式下载完成！检查 example_medium/ 目录"
    echo ""
fi

# 示例4: 完整模式（仅显示命令，不自动运行）
echo "=== 示例4: 完整模式 ==="
echo "下载所有基因组的所有注释文件（数据量大，需要较长时间）"
echo "命令: python3 download_eggo_improved.py metadata-v2.tsv eggo_full 10 full"
echo ""
echo "注意: 完整模式会下载大量数据，建议在网络稳定且有充足时间时运行"
echo "如需运行完整模式，请手动执行上述命令"
echo ""

# 显示使用提示
echo "=== 使用提示 ==="
echo "1. 查看实时日志: tail -f download_improved_log.txt"
echo "2. 查看下载报告: cat [输出目录]/download_summary_[模式].json"
echo "3. 如果下载中断，重新运行相同命令会自动跳过已下载的文件"
echo "4. 根据网络状况调整线程数（max_workers参数）"
echo ""

echo "=== 文件结构说明 ==="
echo "下载完成后，文件结构如下："
echo "输出目录/"
echo "├── common_annotations/     # 通用注释文件"
echo "├── GCA_xxxxxxx.x/         # 各基因组特定目录"
echo "├── download_summary_*.json # 下载报告"
echo "└── download_improved_log.txt # 详细日志"
echo ""

echo "脚本运行示例完成！"
echo "更多详细信息请查看 使用指南.md 和 README.md"
