# EGGO数据库注释文件下载脚本使用指南

## 脚本概述

我已经为您创建了三个版本的EGGO数据库注释文件下载脚本：

1. **`download_eggo_annotations.py`** - 基础版本
2. **`test_download.py`** - 简化测试版本
3. **`download_eggo_improved.py`** - 改进版本（推荐使用）

## 推荐使用改进版本

**`download_eggo_improved.py`** 是最推荐的版本，具有以下优势：

### 主要特性
- ✅ **多线程并发下载**：提高下载效率
- ✅ **智能重试机制**：自动重试失败的下载
- ✅ **分级下载模式**：根据需要选择下载范围
- ✅ **断点续传**：跳过已存在的文件
- ✅ **详细日志记录**：完整的下载过程记录
- ✅ **超时优化**：根据文件大小设置不同超时时间
- ✅ **错误处理**：优雅处理网络错误

### 下载模式说明

| 模式 | 文件类型 | 文件数量 | 适用场景 |
|------|----------|----------|----------|
| `test` | 5个小文件 | ~5个 | 测试网络连接和脚本功能 |
| `small` | 小文件 | ~8个 | 快速获取基本注释信息 |
| `medium` | 小文件+中等文件 | ~15个 | 获取大部分注释信息 |
| `full` | 所有文件 | ~50个 | 完整的EGGO数据集 |

## 使用方法

### 1. 基本测试（推荐先运行）

```bash
# 测试网络连接和脚本功能，只下载3个基因组的5个小文件
python3 download_eggo_improved.py metadata-v2.tsv test_output 3 test 3
```

### 2. 小规模下载

```bash
# 下载前10个基因组的小文件
python3 download_eggo_improved.py metadata-v2.tsv eggo_small 5 small 10
```

### 3. 中等规模下载

```bash
# 下载前100个基因组的小文件和中等文件
python3 download_eggo_improved.py metadata-v2.tsv eggo_medium 8 medium 100
```

### 4. 完整下载

```bash
# 下载所有基因组的所有注释文件
python3 download_eggo_improved.py metadata-v2.tsv eggo_full 10 full
```

## 参数详解

```bash
python3 download_eggo_improved.py <metadata_file> [output_dir] [max_workers] [mode] [limit]
```

- **metadata_file**: metadata-v2.tsv文件路径（必需）
- **output_dir**: 输出目录（可选，默认: eggo_annotations）
- **max_workers**: 最大线程数（可选，默认: 5，建议3-10）
- **mode**: 下载模式（可选，默认: full）
  - `test`: 测试模式，只下载5个小文件
  - `small`: 小文件模式，快速完成
  - `medium`: 中等模式，包含小文件和中等文件
  - `full`: 完整模式，下载所有文件
- **limit**: 限制处理的基因组数量（可选，用于测试）

## 输出结构

```
eggo_annotations/
├── common_annotations/              # 通用注释文件（所有基因组共享）
│   ├── EGGO.csv                    # 主要EGGO数据库
│   ├── CAZyDB.07302020.fam-activities.txt
│   ├── cog-20.def.tab
│   └── ...
├── GCA_000219855.1/                # 基因组特定目录
│   ├── EGGO.csv                    # 该基因组的注释文件副本
│   ├── CAZyDB.07302020.fam-activities.txt
│   ├── ...
│   └── GCA_000219855.1_download_report.json  # 下载报告
├── GCA_000283575.1/
│   └── ...
├── download_summary_[mode].json     # 总体下载报告
└── download_improved_log.txt        # 详细日志
```

## 文件说明

### 核心数据文件
- `EGGO.csv` - 主要的EGGO数据库文件（包含生长率预测）
- `EGGO.RData` - R格式的EGGO数据库

### 功能注释文件
- `CAZyDB.07302020.fam-activities.txt` - CAZy数据库家族活性
- `cog-20.def.tab` - COG功能定义
- `fun-20.tab` - 功能分类表
- `ribosomal_protein_COGs.txt` - 核糖体蛋白COG

### 分类信息文件
- `condensed_species_NCBI.csv` - NCBI物种信息
- `gtdbtk.bac120.summary.tsv` - GTDB细菌分类
- `gtdbtk.ar122.summary.tsv` - GTDB古菌分类

### 基因组统计文件
- `genome_lengths_vs.txt` - 基因组长度
- `gc_and_length_vs.tsv` - GC含量和长度
- `n_proteins.tbl` - 蛋白质数量

## 监控下载进度

### 查看实时日志
```bash
tail -f download_improved_log.txt
```

### 查看下载统计
```bash
# 查看总体报告
cat eggo_annotations/download_summary_test.json | python3 -m json.tool

# 查看特定基因组报告
cat eggo_annotations/GCA_000219855.1/GCA_000219855.1_download_report.json | python3 -m json.tool
```

## 性能优化建议

### 网络优化
- **线程数**: 根据网络带宽调整，建议3-10个线程
- **模式选择**: 先用test模式验证，再选择合适的模式
- **分批下载**: 对于大量基因组，可以分批处理

### 存储优化
- **磁盘空间**: 完整下载可能需要数GB空间
- **文件去重**: common_annotations目录包含通用文件，避免重复

## 故障排除

### 常见问题

1. **网络超时**
   ```bash
   # 减少线程数，增加稳定性
   python3 download_eggo_improved.py metadata-v2.tsv output 3 small 10
   ```

2. **部分文件下载失败**
   ```bash
   # 重新运行脚本，会自动跳过已下载的文件
   python3 download_eggo_improved.py metadata-v2.tsv output 5 medium
   ```

3. **磁盘空间不足**
   ```bash
   # 使用small模式，只下载必要文件
   python3 download_eggo_improved.py metadata-v2.tsv output 5 small
   ```

### 错误日志分析
- 查看 `download_improved_log.txt` 获取详细错误信息
- 检查 `download_summary_[mode].json` 了解整体下载状态

## 实际使用建议

### 第一次使用
1. 先运行test模式验证环境：
   ```bash
   python3 download_eggo_improved.py metadata-v2.tsv test_run 3 test 5
   ```

2. 根据需要选择合适模式：
   - 只需要基本信息：使用 `small` 模式
   - 需要较完整数据：使用 `medium` 模式
   - 需要完整数据集：使用 `full` 模式

### 大规模下载
1. 分批处理，避免一次性下载过多
2. 监控磁盘空间和网络状况
3. 定期检查日志文件

## 技术支持

如果遇到问题，请检查：
1. 网络连接是否稳定
2. 磁盘空间是否充足
3. Python依赖是否安装完整
4. 日志文件中的错误信息

脚本已经过测试验证，具有良好的错误处理和重试机制，能够在网络不稳定的情况下正常工作。
