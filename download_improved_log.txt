2025-07-10 20:44:10,598 - MainThread - INFO - 开始EGGO注释文件下载任务 (模式: test)
2025-07-10 20:44:10,620 - MainThread - INFO - 成功加载metadata文件，共3条记录
2025-07-10 20:44:10,620 - MainThread - INFO - 开始下载通用EGGO注释文件...
2025-07-10 20:44:10,621 - MainThread - INFO - 正在下载通用文件: CAZyDB.07302020.fam-activities.txt
2025-07-10 20:44:11,288 - MainThread - INFO - 通用文件下载成功: CAZyDB.07302020.fam-activities.txt
2025-07-10 20:44:11,289 - MainThread - INFO - 正在下载通用文件: cog-20.def.tab
2025-07-10 20:45:11,351 - MainThread - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='raw.githubusercontent.com', port=443): Read timed out. (read timeout=60)")': /jlw-ecoevo/eggo/master/Data/cog-20.def.tab
2025-07-10 20:47:12,180 - MainThread - WARNING - 下载错误 (尝试 1/3): cog-20.def.tab - HTTPSConnectionPool(host='raw.githubusercontent.com', port=443): Read timed out.
2025-07-10 20:47:13,863 - MainThread - INFO - 通用文件下载成功: cog-20.def.tab
2025-07-10 20:47:13,864 - MainThread - INFO - 正在下载通用文件: ar14.arCOGdef.tab
2025-07-10 20:47:14,407 - MainThread - INFO - 通用文件下载成功: ar14.arCOGdef.tab
2025-07-10 20:47:14,407 - MainThread - INFO - 正在下载通用文件: fun-20.tab
2025-07-10 20:48:14,470 - MainThread - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='raw.githubusercontent.com', port=443): Read timed out. (read timeout=60)")': /jlw-ecoevo/eggo/master/Data/fun-20.tab
2025-07-10 20:49:15,157 - MainThread - INFO - 通用文件下载成功: fun-20.tab
2025-07-10 20:49:15,158 - MainThread - INFO - 正在下载通用文件: ribosomal_protein_COGs.txt
2025-07-10 20:50:15,218 - MainThread - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='raw.githubusercontent.com', port=443): Read timed out. (read timeout=60)")': /jlw-ecoevo/eggo/master/Data/ribosomal_protein_COGs.txt
